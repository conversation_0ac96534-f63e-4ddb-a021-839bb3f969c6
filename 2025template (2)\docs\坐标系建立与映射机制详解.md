# STM32步进电机控制系统坐标系建立与映射机制详解

## 核心问题解答：这个系统如何建立坐标系？

### 🎯 系统中存在的三套坐标系

## 1. 视觉坐标系（像素坐标系）

### 📐 **坐标系定义**
```
视觉模块(MaixCam)的图像坐标系：
┌─────────────────────────────────┐ ← Y=0
│  (0,0)                    (640,0)│
│    ┌─────────────────────────┐   │
│    │                         │   │
│    │    red:(320,240)        │   │ ← 图像中心区域
│    │    gre:(350,260)        │   │
│    │                         │   │
│    └─────────────────────────┘   │
│ (0,480)                 (640,480)│ ← Y=480
└─────────────────────────────────┘
    ↑                           ↑
   X=0                        X=640
```

### 📊 **数据格式和范围**
```c
// 视觉模块发送的数据格式
"red:(320,240)\n"    // 红色激光在像素坐标(320,240)
"gre:(350,260)\n"    // 绿色激光在像素坐标(350,260)

// 坐标范围（假设640x480分辨率）
X轴范围: 0 ~ 640 像素
Y轴范围: 0 ~ 480 像素
原点位置: 图像左上角(0,0)
```

## 2. 步进电机坐标系（脉冲/角度坐标系）

### ⚙️ **电机坐标系定义**
```c
// 步进电机的16位位置系统
位置范围: 0 ~ 65535 脉冲
角度范围: 0° ~ 360°
精度: 360° / 65536 = 0.0055°/脉冲

// 角度计算公式
float calc_motor_angle(uint8_t dir, uint32_t position)
{
    position = position % 65536;  // 确保在16位范围内
    float angle = ((float)position * 360.0f) / 65536.0f;
    if (dir) angle = -angle;  // 考虑方向
    return angle;
}
```

### 🔄 **相对坐标系建立**
```c
// 系统启动时建立参考坐标系
uint32_t x_reference_position = 0;  // X轴参考位置
uint32_t y_reference_position = 0;  // Y轴参考位置
float x_relative_angle = 0.0f;      // X轴相对角度
float y_relative_angle = 0.0f;      // Y轴相对角度

// 相对角度计算
float calc_relative_angle(uint8_t dir, uint32_t current_position, uint32_t reference_position)
{
    // 计算相对于开机位置的角度偏移
    int32_t relative_position;
    if (current_position >= reference_position) {
        relative_position = current_position - reference_position;
    } else {
        relative_position = 65536 - reference_position + current_position;
    }
    
    // 选择最短路径（避免超过半圈）
    if (relative_position > 32768) {
        relative_position = relative_position - 65536;
    }
    
    float angle = ((float)relative_position * 360.0f) / 65536.0f;
    if (dir) angle = -angle;
    return angle;
}
```

## 3. 物理坐标系（机械运动坐标系）

### 🏗️ **物理坐标系特征**
```
机械限位: ±50度
X轴电机: 控制水平方向运动
Y轴电机: 控制垂直方向运动

物理坐标系：
        Y轴正方向
            ↑
            │
            │
────────────┼────────────→ X轴正方向
            │
            │
            ↓
        Y轴负方向

角度限制: -50° ≤ 相对角度 ≤ +50°
```

## 4. 坐标系映射与转换机制

### 🔗 **关键映射关系**

#### 4.1 视觉坐标 → 控制误差
```c
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;
    
    // 关键映射：使用绿色激光作为目标，红色激光作为当前位置
    pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    
    // 控制量转换为电机速度（注意X轴取负）
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}
```

#### 4.2 控制逻辑分析
```
目标设定: 绿色激光坐标 (target)
当前位置: 红色激光坐标 (feedback)
控制误差: error = target - feedback

PID控制器计算:
X轴误差 = green_x - red_x
Y轴误差 = green_y - red_y

电机控制:
X轴速度 = -PID_X_output  (注意负号!)
Y轴速度 = PID_Y_output
```

### 🎯 **坐标系建立的关键步骤**

#### 步骤1: 系统初始化时建立参考坐标
```c
void save_initial_position(void)
{
    if (!initial_position_saved)
    {
        // 读取X轴当前位置作为参考点
        Emm_V5_Read_Sys_Params(&MOTOR_X_UART, MOTOR_X_ADDR, S_CPOS);
        // 读取Y轴当前位置作为参考点  
        Emm_V5_Read_Sys_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, S_CPOS);
        
        my_printf(&huart1, "正在读取初始位置...\r\n");
    }
}
```

#### 步骤2: 建立参考坐标系
```c
// 在parse_x_motor_data中建立参考系
if (!x_reference_initialized)
{
    x_reference_position = resp->position;  // 设置参考位置
    x_reference_initialized = 1;
    x_relative_angle = 0.0f;               // 相对角度清零
    my_printf(&huart1, "X轴参考位置已初始化: %ld 脉冲\r\n", x_reference_position);
}
```

#### 步骤3: 实时坐标转换
```c
// 每次收到电机位置反馈时更新相对坐标
x_relative_angle = calc_relative_angle(resp->dir, resp->position, x_reference_position);
y_relative_angle = calc_relative_angle(resp->dir, resp->position, y_reference_position);
```

## 5. 坐标系映射的工作原理

### 🔄 **完整的坐标转换链路**

```
视觉检测 → 像素坐标 → PID控制 → 电机速度 → 角度变化 → 物理位移
    ↓           ↓          ↓         ↓         ↓         ↓
MaixCam → (320,240) → error=-30 → -1.5RPM → -2.7° → 激光移动
```

### 📊 **数据流向详解**

```mermaid
graph TD
    A[视觉模块检测激光] --> B[像素坐标 red/gre]
    B --> C[PID控制器计算误差]
    C --> D[输出电机控制量]
    D --> E[步进电机角度变化]
    E --> F[物理位置改变]
    F --> G[视觉反馈新坐标]
    G --> A
    
    H[电机位置反馈] --> I[角度计算]
    I --> J[相对坐标更新]
    J --> K[安全限位检查]
```

## 6. 关键设计思想

### 🎯 **为什么这样设计坐标系？**

#### 6.1 多坐标系的必要性
- **视觉坐标系**: 便于图像处理和目标识别
- **电机坐标系**: 便于精确控制和位置反馈  
- **物理坐标系**: 便于理解实际运动和安全限位

#### 6.2 相对坐标系的优势
```c
// 使用相对坐标而不是绝对坐标的原因：
1. 消除开机位置差异
2. 便于设置运动限位（±50度）
3. 避免绝对位置的累积误差
4. 简化复位操作
```

#### 6.3 坐标映射策略
```c
// 为什么X轴取负号？
Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);

可能的原因：
1. 电机安装方向与视觉坐标系方向相反
2. 机械结构导致的运动方向映射
3. 确保控制系统的稳定性
```

## 7. 坐标系标定与校准

### 🔧 **系统标定流程**

#### 7.1 初始位置标定
```c
// 开机时自动保存初始位置
save_initial_position();  // 读取当前电机位置作为参考点

// 手动复位到初始位置
void process_reset_command(void)
{
    if (initial_position_saved)
    {
        // 使用绝对位置模式回到初始位置
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_initial_direction, 
                           MOTOR_MAX_SPEED/2, MOTOR_ACCEL, x_initial_position, 
                           true, MOTOR_SYNC_FLAG);
    }
}
```

#### 7.2 视觉-物理坐标映射
```c
// PID参数决定了坐标映射的响应特性
PID_struct_init(&pid_x, POSITION_PID, 3, 1, 0.02, 0, 0);  // X轴PID
PID_struct_init(&pid_y, POSITION_PID, 3, 1, 0.02, 0, 0);  // Y轴PID

// 这些参数决定了：
// - 像素误差如何转换为电机速度
// - 系统的响应速度和稳定性
// - 坐标映射的线性度
```

## 8. 坐标系的实际应用

### 🎯 **典型工作场景**

```
场景：激光跟踪控制
1. 视觉模块检测到：red:(300,200), gre:(350,250)
2. PID计算误差：error_x = 350-300 = 50, error_y = 250-200 = 50  
3. PID输出控制量：out_x = 2.5, out_y = 2.5
4. 电机执行：X轴 = -2.5RPM, Y轴 = 2.5RPM
5. 物理运动：激光点向目标移动
6. 视觉反馈：red:(325,225), gre:(350,250)
7. 误差减小：error_x = 25, error_y = 25
8. 继续调整直到误差接近零
```

## 9. 实际数值计算示例

### 📊 **完整的坐标转换计算过程**

#### 示例场景：激光点跟踪控制

```c
// 假设系统状态：
// 1. 视觉检测结果
latest_red_laser_coord.x = 300;    // 红色激光X坐标
latest_red_laser_coord.y = 200;    // 红色激光Y坐标
latest_green_laser_coord.x = 350;  // 绿色激光X坐标（目标）
latest_green_laser_coord.y = 250;  // 绿色激光Y坐标（目标）

// 2. PID控制计算（在pi_proc中执行）
float error_x = 350 - 300 = 50;    // X轴像素误差
float error_y = 250 - 200 = 50;    // Y轴像素误差

// 3. PID输出（假设Kp=3, Ki=1, Kd=0.02）
float pid_out_x = 3 * 50 = 150;    // 简化计算，实际还有积分和微分项
float pid_out_y = 3 * 50 = 150;

// 4. 电机速度设置
Step_Motor_Set_Speed_my(-150, 150); // X轴取负，Y轴正向

// 5. 电机实际执行（假设最大速度3RPM）
// 由于输出150超过最大值3，会被限制到3RPM
X轴实际速度: -3 RPM (逆时针)
Y轴实际速度: +3 RPM (顺时针)
```

### 🔢 **电机角度变化计算**

```c
// 假设电机运行1秒后的位置变化
// 3RPM = 3转/分 = 0.05转/秒 = 18度/秒

// 电机位置反馈（假设值）
X轴新位置: 原位置 - 18度 = 相对角度 -18度
Y轴新位置: 原位置 + 18度 = 相对角度 +18度

// 16位位置值计算
// 18度对应的脉冲数 = 18 * 65536 / 360 = 3277 脉冲
```

### 📐 **坐标系映射关系推导**

```c
// 关键问题：像素误差如何映射到物理角度？

假设映射关系（需要实际标定确定）：
1像素误差 ≈ 0.1度物理角度变化

那么：
50像素误差 → 5度角度变化需求
PID输出150 → 限制到3RPM → 实际18度/秒变化

// 这说明系统可能存在：
1. PID参数过大，导致输出饱和
2. 需要调整Kp参数以获得合适的响应
3. 可能需要增加积分项来消除稳态误差
```

## 10. 坐标系建立的关键代码分析

### 🔍 **初始化序列详解**

```c
// main.c中的初始化顺序
int main(void)
{
    // ... 基础初始化 ...

    // 1. PID控制器初始化
    PID_INIT();  // 设置PID参数

    // 2. 环形缓冲区初始化
    rt_ringbuffer_init(&ringbuffer_x, ringbuffer_pool_x, sizeof(ringbuffer_pool_x));
    rt_ringbuffer_init(&ringbuffer_y, ringbuffer_pool_y, sizeof(ringbuffer_pool_y));
    rt_ringbuffer_init(&ringbuffer_pi, ringbuffer_pool_pi, sizeof(ringbuffer_pool_pi));

    // 3. 步进电机初始化
    Step_Motor_Init();  // 使能电机

    // 4. 位置清零（建立电机坐标系原点）
    Emm_V5_Reset_CurPos_To_Zero(&huart4, 0x01);  // Y轴清零
    Emm_V5_Reset_CurPos_To_Zero(&huart2, 0x01);  // X轴清零

    // 5. 保存初始位置（建立参考坐标系）
    save_initial_position();  // 读取并保存当前位置作为参考点

    // 6. 进入主循环
    while (1) {
        schedule_run();  // 执行任务调度
    }
}
```

### 🎯 **坐标系同步机制**

```c
// 关键：三个坐标系如何保持同步？

// 1. 视觉坐标系更新（异步，由视觉模块驱动）
// 通过UART6接收 "red:(x,y)" 和 "gre:(x,y)"

// 2. 电机坐标系更新（1ms周期，uart_proc驱动）
// 通过UART2/4接收电机位置反馈

// 3. 控制坐标系更新（20ms周期，pi_proc驱动）
// 使用最新的视觉坐标计算控制量

// 同步策略：
// - 使用全局变量存储最新坐标
// - 各模块异步更新，PID控制器使用最新值
// - 通过时间戳或有效标志确保数据新鲜度
```

## 11. 坐标系设计的优缺点分析

### ✅ **设计优点**

1. **模块化清晰**：各坐标系职责明确，便于维护
2. **实时性好**：异步更新，响应速度快
3. **精度高**：16位电机位置，0.0055度精度
4. **安全可靠**：相对坐标+角度限位，防止机械损坏

### ⚠️ **潜在问题**

1. **坐标映射未标定**：像素到角度的映射关系不明确
2. **PID参数可能不当**：输出容易饱和
3. **缺少坐标变换矩阵**：视觉坐标到物理坐标的转换过于简单
4. **没有坐标系校准机制**：无法动态调整映射关系

### 🔧 **改进建议**

```c
// 1. 增加坐标映射标定功能
typedef struct {
    float pixel_to_angle_x;  // X轴像素到角度转换系数
    float pixel_to_angle_y;  // Y轴像素到角度转换系数
    float offset_x;          // X轴偏移量
    float offset_y;          // Y轴偏移量
} CoordMapping_t;

// 2. 增加坐标变换函数
void pixel_to_physical(int pixel_x, int pixel_y, float *angle_x, float *angle_y)
{
    *angle_x = (pixel_x - 320) * mapping.pixel_to_angle_x + mapping.offset_x;
    *angle_y = (pixel_y - 240) * mapping.pixel_to_angle_y + mapping.offset_y;
}

// 3. 增加在线标定功能
void calibrate_coordinate_mapping(void)
{
    // 通过已知的像素位置和对应的物理角度来计算映射系数
}
```

**总结**：这个系统通过建立三套坐标系（视觉、电机、物理）和相应的映射关系，实现了从视觉检测到精确物理控制的完整闭环。关键在于理解各坐标系的作用和相互转换关系，以及如何通过PID控制器实现坐标系之间的动态映射。
