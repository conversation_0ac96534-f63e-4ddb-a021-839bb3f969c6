# 数据传回函数调用链路完整追踪

## 🎯 核心问题：数据传回来后哪个函数被调用，怎么运行的？

让我为您逐步追踪三种不同数据的完整调用链路：

## 1. 步进电机数据传回调用链路

### 📡 **第一步：硬件中断接收数据**

```c
// 当UART2/4接收到数据时，硬件自动触发中断
// 中断服务函数：stm32f4xx_it.c

void UART2_IRQHandler(void)  // X轴电机数据中断
{
    HAL_UART_IRQHandler(&huart2);  // 调用HAL库中断处理
}

void UART4_IRQHandler(void)  // Y轴电机数据中断  
{
    HAL_UART_IRQHandler(&huart4);  // 调用HAL库中断处理
}
```

### 📥 **第二步：HAL库回调函数**

```c
// HAL库处理完中断后，会调用用户定义的回调函数
// 这个函数需要用户在某处实现（通常在main.c或相关文件中）

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2)  // X轴电机数据
    {
        // 将接收到的数据存入X轴环形缓冲区
        rt_ringbuffer_put(&ringbuffer_x, motor_x_buf, 1);
        
        // 重新启动接收（准备接收下一个字节）
        HAL_UART_Receive_IT(&huart2, motor_x_buf, 1);
    }
    else if (huart->Instance == UART4)  // Y轴电机数据
    {
        // 将接收到的数据存入Y轴环形缓冲区
        rt_ringbuffer_put(&ringbuffer_y, motor_y_buf, 1);
        
        // 重新启动接收
        HAL_UART_Receive_IT(&huart4, motor_y_buf, 1);
    }
}
```

### ⏰ **第三步：定时器触发主循环处理**

```c
// main.c主循环，每1ms执行一次
int main(void)
{
    // ... 初始化代码 ...
    
    while (1)
    {
        schedule_run();  // 调用任务调度器
    }
}

// schedule.c - 任务调度器
void schedule_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();  // 获取当前时间
        
        // 检查是否到了执行时间
        if (now_time >= schedule_task[i].rate_ms + schedule_task[i].last_run)
        {
            schedule_task[i].last_run = now_time;
            schedule_task[i].task_func();  // 执行任务函数
        }
    }
}

// 任务配置表
static schedule_task_t schedule_task[] = {
    {uart_proc, 1, 0},    // uart_proc每1ms执行一次
    {pi_proc, 20, 0}      // pi_proc每20ms执行一次
};
```

### 🔍 **第四步：uart_proc函数处理数据**

```c
// bsp/uart_bsp.c - 核心数据处理函数
void uart_proc(void)
{
    uint16_t length_x, length_y;
    Emm_V5_Response_t resp_x, resp_y;
    
    // ========== 处理X轴电机数据 ==========
    length_x = rt_ringbuffer_data_len(&ringbuffer_x);  // 检查缓冲区数据长度
    my_printf(&huart1, "X轴缓冲区数据长度: %d\n", length_x);  // 调试输出
    
    if (length_x > 0)  // 如果有数据
    {
        // 从环形缓冲区取出数据
        rt_ringbuffer_get(&ringbuffer_x, output_buffer_x, length_x);
        output_buffer_x[length_x] = '\0';  // 添加字符串结束符
        
        my_printf(&huart1, "X轴原始数据: ");
        for(int i = 0; i < length_x; i++) {
            my_printf(&huart1, "0x%02X ", output_buffer_x[i]);
        }
        my_printf(&huart1, "\n");
        
        // 调用Emm_V5协议解析函数
        if (Emm_V5_Parse_Response(output_buffer_x, length_x, &resp_x))
        {
            my_printf(&huart1, "X轴数据解析成功，调用parse_x_motor_data\n");
            parse_x_motor_data(&resp_x);  // 解析成功，处理数据
        }
        else
        {
            my_printf(&huart1, "X轴数据解析失败!\r\n");
        }
        
        // 清空输出缓冲区
        memset(output_buffer_x, 0, length_x);
    }
    
    // ========== 处理Y轴电机数据 ==========
    // 与X轴处理流程完全相同...
    length_y = rt_ringbuffer_data_len(&ringbuffer_y);
    if (length_y > 0)
    {
        rt_ringbuffer_get(&ringbuffer_y, output_buffer_y, length_y);
        output_buffer_y[length_y] = '\0';
        
        if (Emm_V5_Parse_Response(output_buffer_y, length_y, &resp_y))
        {
            parse_y_motor_data(&resp_y);  // 处理Y轴数据
        }
        else
        {
            my_printf(&huart1, "Y轴数据解析失败!\r\n");
        }
        
        memset(output_buffer_y, 0, length_y);
    }
}
```

### 🔧 **第五步：Emm_V5_Parse_Response协议解析**

```c
// app/Emm_V5.c - 协议解析函数
uint8_t Emm_V5_Parse_Response(uint8_t *buffer, uint8_t len, Emm_V5_Response_t *resp)
{
    my_printf(&huart1, "开始解析Emm_V5协议，数据长度: %d\n", len);
    
    if (len < 4) {  // 最小数据包长度检查
        my_printf(&huart1, "数据包太短，解析失败\n");
        return 0;
    }
    
    // 解析数据包结构（具体协议格式需要查看Emm_V5文档）
    resp->addr = buffer[0];      // 电机地址
    resp->func = buffer[1];      // 功能码
    
    my_printf(&huart1, "解析结果 - 地址: 0x%02X, 功能码: 0x%02X\n", 
              resp->addr, resp->func);
    
    // 根据功能码解析不同的数据内容
    switch (resp->func)
    {
        case 0x36:  // 读取实时位置
            // 解析位置数据（4字节）
            resp->position = (buffer[2] << 24) | (buffer[3] << 16) | 
                           (buffer[4] << 8) | buffer[5];
            resp->dir = buffer[6];  // 方向
            my_printf(&huart1, "位置数据: %ld, 方向: %d\n", 
                      resp->position, resp->dir);
            break;
            
        case 0x35:  // 读取实时转速
            resp->speed = (buffer[2] << 8) | buffer[3];
            my_printf(&huart1, "转速数据: %d RPM\n", resp->speed);
            break;
            
        // ... 其他功能码处理
    }
    
    resp->valid = 1;  // 标记数据有效
    my_printf(&huart1, "协议解析完成\n");
    return 1;  // 解析成功
}
```

### 📊 **第六步：parse_x_motor_data数据处理**

```c
// bsp/uart_bsp.c - X轴数据处理函数
void parse_x_motor_data(Emm_V5_Response_t *resp)
{
    my_printf(&huart1, "进入parse_x_motor_data，功能码: 0x%02X\n", resp->func);
    
    // 根据功能码处理不同类型的数据
    switch (resp->func)
    {
    case 0x36: // 读取实时位置 - 最重要的处理
        my_printf(&huart1, "处理X轴位置数据\n");
        
        // 计算X轴绝对角度
        x_motor_angle = calc_motor_angle(resp->dir, resp->position);
        my_printf(&huart1, "计算绝对角度: %.2f度\n", x_motor_angle);

        // 初始化参考位置或计算相对角度
        if (!x_reference_initialized)
        {
            my_printf(&huart1, "初始化X轴参考位置\n");
            x_reference_position = resp->position;
            x_reference_initialized = 1;
            x_relative_angle = 0.0f;
            my_printf(&huart1, "X轴参考位置已初始化: %ld 脉冲\r\n", x_reference_position);
        }
        else
        {
            // 计算相对角度
            my_printf(&huart1, "计算X轴相对角度\n");
            x_relative_angle = calc_relative_angle(resp->dir, resp->position, x_reference_position);
            my_printf(&huart1, "相对角度: %.2f度\n", x_relative_angle);
        }
        
        // 输出完整位置信息
        my_printf(&huart1, "X轴电机地址:%d 实时位置:%ld 脉冲 绝对角度:%.2f° 相对角度:%.2f°\r\n",
                  resp->addr, resp->position, x_motor_angle, x_relative_angle);
        break;

    case 0x35: // 读取实时转速
        my_printf(&huart1, "处理X轴转速数据\n");
        my_printf(&huart1, "X轴电机地址:%d 实时转速:%d RPM\r\n", resp->addr, resp->speed);
        break;

    case 0x33: // 读取电机状态
        my_printf(&huart1, "处理X轴状态数据\n");
        my_printf(&huart1, "X轴电机地址:%d 状态值:0x%02X\r\n", resp->addr, resp->status);
        
        // 解析状态位
        if (resp->status & 0x01) my_printf(&huart1, "  X轴电机使能\r\n");
        if (resp->status & 0x02) my_printf(&huart1, "  X轴电机已到位\r\n");
        if (resp->status & 0x04) my_printf(&huart1, "  X轴电机正转中\r\n");
        break;

    default:
        my_printf(&huart1, "未知功能码: 0x%02X\n", resp->func);
        break;
    }
    
    my_printf(&huart1, "parse_x_motor_data处理完成\n");
}
```

### 🧮 **第七步：角度计算函数**

```c
// bsp/uart_bsp.c - 角度计算函数
float calc_motor_angle(uint8_t dir, uint32_t position)
{
    my_printf(&huart1, "计算电机角度 - 位置: %ld, 方向: %d\n", position, dir);
    
    float angle;
    // 确保位置值在0-65535范围内
    position = position % 65536;
    my_printf(&huart1, "标准化位置: %ld\n", position);

    // 计算角度值 (16位精度对应360度)
    angle = ((float)position * 360.0f) / 65536.0f;
    my_printf(&huart1, "计算角度: %.2f度\n", angle);

    // 根据方向，角度取负
    if (dir) {
        angle = -angle;
        my_printf(&huart1, "方向校正后角度: %.2f度\n", angle);
    }

    return angle;
}

float calc_relative_angle(uint8_t dir, uint32_t current_position, uint32_t reference_position)
{
    my_printf(&huart1, "计算相对角度 - 当前: %ld, 参考: %ld\n", 
              current_position, reference_position);
    
    // 确保位置值在0-65535范围内
    current_position = current_position % 65536;
    reference_position = reference_position % 65536;

    // 计算相对位置差
    int32_t relative_position;
    if (current_position >= reference_position) {
        relative_position = current_position - reference_position;
    } else {
        // 处理数值环绕
        relative_position = 65536 - reference_position + current_position;
    }
    
    my_printf(&huart1, "相对位置差: %ld\n", relative_position);

    // 选择较短路径 (避免超过半圈的计算)
    if (relative_position > 32768) {
        relative_position = relative_position - 65536;
        my_printf(&huart1, "选择短路径: %ld\n", relative_position);
    }

    // 转换为角度
    float angle = ((float)relative_position * 360.0f) / 65536.0f;
    my_printf(&huart1, "相对角度: %.2f度\n", angle);

    // 考虑方向
    if (dir) {
        angle = -angle;
        my_printf(&huart1, "方向校正后相对角度: %.2f度\n", angle);
    }

    return angle;
}
```

## 2. 完整调用链路时序图

```
时间轴:    0μs     100μs    1000μs   1001μs   1002μs
           |        |        |        |        |
硬件中断:   ↓        
         UART2_IRQHandler()
           ↓
HAL回调:   HAL_UART_RxCpltCallback()
           ↓
存入缓冲:   rt_ringbuffer_put()
           
                             ↓
定时调度:                   schedule_run()
                             ↓
任务执行:                   uart_proc()
                             ↓
数据处理:                   rt_ringbuffer_get()
                                      ↓
协议解析:                            Emm_V5_Parse_Response()
                                      ↓
数据处理:                            parse_x_motor_data()
                                               ↓
角度计算:                                    calc_motor_angle()
                                               ↓
状态更新:                                    更新全局变量
```

## 3. 视觉模块数据传回调用链路

### 📡 **视觉数据接收处理流程**

```c
// 第一步：UART6中断接收视觉数据
void UART6_IRQHandler(void)
{
    HAL_UART_IRQHandler(&huart6);  // 处理UART6中断
}

// 第二步：HAL回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART6)  // 视觉模块数据
    {
        // 将接收到的字符存入视觉数据环形缓冲区
        rt_ringbuffer_put(&ringbuffer_pi, pi_rx_buf, 1);

        // 重新启动接收
        HAL_UART_Receive_IT(&huart6, pi_rx_buf, 1);
    }
}

// 第三步：uart_proc中处理视觉数据
void uart_proc(void)
{
    // ... 处理电机数据 ...

    // ========== 处理视觉模块数据 ==========
    length_pi = rt_ringbuffer_data_len(&ringbuffer_pi);
    my_printf(&huart1, "视觉数据缓冲区长度: %d\n", length_pi);

    if(length_pi > 0)
    {
        rt_ringbuffer_get(&ringbuffer_pi, output_buffer_pi, length_pi);

        // 逐字节处理，寻找完整的行
        for (int i = 0; i < length_pi; i++)
        {
            char current_char = output_buffer_pi[i];
            my_printf(&huart1, "处理字符: '%c' (0x%02X)\n", current_char, current_char);

            // 将当前字符添加到行缓冲器
            if (line_buffer_idx < sizeof(line_buffer) - 1)
            {
                line_buffer[line_buffer_idx++] = current_char;
            }
            else
            {
                my_printf(&huart1, "行缓冲区溢出，丢弃数据\n");
                line_buffer_idx = 0;
                continue;
            }

            // 检查是否收到换行符 ('\n')
            if (current_char == '\n')
            {
                line_buffer[line_buffer_idx] = '\0';  // 添加字符串结束符

                my_printf(&huart1, "收到完整行: '%s'\n", line_buffer);

                // 调用视觉数据解析函数
                int result = pi_parse_data(line_buffer);

                if (result != 0)
                {
                    my_printf(&huart1, "视觉数据解析错误，错误码: %d\n", result);
                }
                else
                {
                    my_printf(&huart1, "视觉数据解析成功\n");
                }

                line_buffer_idx = 0;  // 重置行缓冲器
            }
        }
    }
}

// 第四步：pi_parse_data解析视觉坐标
int pi_parse_data(char *buffer)
{
    my_printf(&huart1, "开始解析视觉数据: '%s'\n", buffer);

    if (!buffer) {
        my_printf(&huart1, "空指针错误\n");
        return -1;
    }

    int parsed_x, parsed_y;
    int parsed_count;

    // 解析红色激光坐标 "red:(x,y)"
    if (strncmp(buffer, "red:", 4) == 0)
    {
        my_printf(&huart1, "检测到红色激光数据\n");
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);

        if (parsed_count != 2) {
            my_printf(&huart1, "红色激光数据格式错误\n");
            return -2;
        }

        // 更新全局红色激光坐标
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

        my_printf(&huart1, "红色激光坐标更新: X=%d, Y=%d\r\n", parsed_x, parsed_y);
    }
    // 解析绿色激光坐标 "gre:(x,y)"
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        my_printf(&huart1, "检测到绿色激光数据\n");
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);

        if (parsed_count != 2) {
            my_printf(&huart1, "绿色激光数据格式错误\n");
            return -2;
        }

        // 更新全局绿色激光坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        my_printf(&huart1, "绿色激光坐标更新: X=%d, Y=%d\r\n", parsed_x, parsed_y);
    }
    else
    {
        my_printf(&huart1, "未知数据格式: '%s'\n", buffer);
        return -3;
    }

    my_printf(&huart1, "视觉数据解析完成\n");
    return 0;
}
```

## 4. PID控制调用链路

### 🎯 **每20ms执行的PID控制流程**

```c
// 第一步：定时调度触发pi_proc
// schedule.c中每20ms调用一次
void schedule_run(void)
{
    // 检查pi_proc是否到了执行时间
    if (now_time >= 20 + pi_proc_last_run)
    {
        my_printf(&huart1, "触发PID控制任务\n");
        pi_proc();  // 调用PID控制函数
        pi_proc_last_run = now_time;
    }
}

// 第二步：pi_proc执行PID控制
void pi_proc(void)
{
    my_printf(&huart1, "开始PID控制计算\n");

    float pos_out_x, pos_out_y = 0;

    // 检查激光坐标数据有效性
    if (!latest_red_laser_coord.isValid || !latest_green_laser_coord.isValid)
    {
        my_printf(&huart1, "激光坐标数据无效，跳过PID计算\n");
        return;
    }

    my_printf(&huart1, "当前坐标 - 红色:(%d,%d), 绿色:(%d,%d)\n",
              latest_red_laser_coord.x, latest_red_laser_coord.y,
              latest_green_laser_coord.x, latest_green_laser_coord.y);

    // X轴PID计算：绿色激光为目标，红色激光为反馈
    pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    my_printf(&huart1, "X轴PID输出: %.2f\n", pos_out_x);

    // Y轴PID计算
    pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    my_printf(&huart1, "Y轴PID输出: %.2f\n", pos_out_y);

    // 发送控制指令给步进电机（注意X轴取负）
    my_printf(&huart1, "发送电机控制指令: X=%.2f, Y=%.2f\n", -pos_out_x, pos_out_y);
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}

// 第三步：pid_calc计算PID输出
float pid_calc(pid_t *pid, float get, float set, uint8_t smoth)
{
    my_printf(&huart1, "PID计算 - 目标:%.2f, 当前:%.2f\n", set, get);

    pid->get = get;
    pid->set = set;

    // 计算误差
    float raw_error = set - get;
    my_printf(&huart1, "原始误差: %.2f\n", raw_error);

    // 应用死区控制
    if (pid->input_deadband != 0 && fabs(raw_error) <= pid->input_deadband)
    {
        pid->err[NOW] = 0;
        pid->iout = 0;
        my_printf(&huart1, "误差在死区内，输出为0\n");
    }
    else
    {
        pid->err[NOW] = raw_error;
    }

    // 位置式PID计算
    if (pid->pid_mode == POSITION_PID)
    {
        // P项计算
        pid->pout = pid->p * pid->err[NOW];
        my_printf(&huart1, "P项: %.2f * %.2f = %.2f\n", pid->p, pid->err[NOW], pid->pout);

        // I项计算
        pid->iout += pid->i * pid->err[NOW];
        abs_limit(&(pid->iout), pid->integral_limit);
        my_printf(&huart1, "I项: %.2f\n", pid->iout);

        // D项计算
        pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);
        my_printf(&huart1, "D项: %.2f * (%.2f - %.2f) = %.2f\n",
                  pid->d, pid->err[NOW], pid->err[LAST], pid->dout);

        // 总输出
        pid->out = pid->pout + pid->iout + pid->dout;
        abs_limit(&(pid->out), pid->max_out);
        my_printf(&huart1, "PID总输出: %.2f\n", pid->out);
    }

    // 更新误差历史
    pid->err[LLAST] = pid->err[LAST];
    pid->err[LAST] = pid->err[NOW];

    return pid->out;
}

// 第四步：Step_Motor_Set_Speed_my控制电机
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm)
{
    my_printf(&huart1, "设置电机速度 - X轴:%.2f RPM, Y轴:%.2f RPM\n", x_rpm, y_rpm);

    uint8_t x_dir, y_dir;
    uint16_t x_speed_scaled, y_speed_scaled;

    // 限制速度范围
    if (x_rpm > MOTOR_MAX_SPEED) x_rpm = MOTOR_MAX_SPEED;
    if (x_rpm < -MOTOR_MAX_SPEED) x_rpm = -MOTOR_MAX_SPEED;
    if (y_rpm > MOTOR_MAX_SPEED) y_rpm = MOTOR_MAX_SPEED;
    if (y_rpm < -MOTOR_MAX_SPEED) y_rpm = -MOTOR_MAX_SPEED;

    // 确定X轴方向和速度
    if (x_rpm >= 0.0f) {
        x_dir = 0;  // CW方向
        x_speed_scaled = (uint16_t)(x_rpm * 10 + 0.5f);
    } else {
        x_dir = 1;  // CCW方向
        x_speed_scaled = (uint16_t)((-x_rpm) * 10 + 0.5f);
    }

    // 确定Y轴方向和速度
    if (y_rpm >= 0.0f) {
        y_dir = 0;  // CW方向
        y_speed_scaled = (uint16_t)(y_rpm * 10 + 0.5f);
    } else {
        y_dir = 1;  // CCW方向
        y_speed_scaled = (uint16_t)((-y_rpm) * 10 + 0.5f);
    }

    my_printf(&huart1, "电机控制参数 - X轴:方向%d,速度%d; Y轴:方向%d,速度%d\n",
              x_dir, x_speed_scaled, y_dir, y_speed_scaled);

    // 发送控制指令给X轴电机
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    // 发送控制指令给Y轴电机
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}
```

## 5. 完整系统调用链路总结

### 🔄 **三种数据流的完整调用链路对比**

| 数据类型 | 接收中断 | 缓冲存储 | 处理函数 | 解析函数 | 最终处理 |
|---------|---------|---------|---------|---------|---------|
| **X轴电机** | `UART2_IRQHandler` | `ringbuffer_x` | `uart_proc` | `Emm_V5_Parse_Response` | `parse_x_motor_data` |
| **Y轴电机** | `UART4_IRQHandler` | `ringbuffer_y` | `uart_proc` | `Emm_V5_Parse_Response` | `parse_y_motor_data` |
| **视觉模块** | `UART6_IRQHandler` | `ringbuffer_pi` | `uart_proc` | `pi_parse_data` | 更新激光坐标 |

### ⏱️ **时间调度关系**

```
系统时钟: 每1ms触发一次schedule_run()
    ↓
uart_proc: 每1ms执行，处理所有串口数据
    ├── 处理X轴电机数据 (如果有)
    ├── 处理Y轴电机数据 (如果有)
    └── 处理视觉模块数据 (如果有)

pi_proc: 每20ms执行，进行PID控制
    ├── 读取最新的激光坐标
    ├── 计算PID控制量
    └── 发送电机控制指令
```

### 📊 **数据流向图**

```
硬件层:
步进电机 ──UART2/4──→ STM32 ──UART6──← 视觉模块
    ↑                    ↓              ↑
    └── 控制指令 ←──── PID控制器 ←── 激光坐标

软件调用链:
中断接收 → HAL回调 → 环形缓冲 → uart_proc → 协议解析 → 数据处理
    ↓           ↓         ↓          ↓         ↓         ↓
UART_IRQHandler → HAL_UART_RxCpltCallback → rt_ringbuffer_put →
    → uart_proc → Emm_V5_Parse_Response/pi_parse_data →
    → parse_x_motor_data/更新坐标 → 全局变量更新

控制循环:
定时调度 → PID计算 → 电机控制 → 位置反馈 → 视觉检测 → 坐标更新
    ↓         ↓        ↓        ↓        ↓        ↓
schedule_run → pi_proc → Step_Motor_Set_Speed_my →
    → Emm_V5_Vel_Control → 电机运动 → 视觉检测 →
    → 新的激光坐标 → 下一轮PID计算
```

### 🎯 **关键函数调用顺序**

#### 电机数据处理链路:
```
1. UART2_IRQHandler() - 硬件中断
2. HAL_UART_RxCpltCallback() - HAL回调
3. rt_ringbuffer_put() - 数据存储
4. uart_proc() - 主处理函数 (1ms周期)
5. rt_ringbuffer_get() - 数据读取
6. Emm_V5_Parse_Response() - 协议解析
7. parse_x_motor_data() - 数据处理
8. calc_motor_angle() - 角度计算
9. 更新全局变量 (x_relative_angle等)
```

#### 视觉数据处理链路:
```
1. UART6_IRQHandler() - 硬件中断
2. HAL_UART_RxCpltCallback() - HAL回调
3. rt_ringbuffer_put() - 数据存储
4. uart_proc() - 主处理函数 (1ms周期)
5. rt_ringbuffer_get() - 数据读取
6. 逐字符处理寻找'\n'
7. pi_parse_data() - 坐标解析
8. sscanf() - 格式化解析
9. 更新全局变量 (latest_red_laser_coord等)
```

#### PID控制链路:
```
1. schedule_run() - 定时调度 (20ms周期)
2. pi_proc() - PID控制函数
3. pid_calc() - PID计算 (X轴和Y轴)
4. Step_Motor_Set_Speed_my() - 速度设置
5. Emm_V5_Vel_Control() - 发送控制指令
6. 电机执行运动
7. 位置反馈 → 重新进入电机数据处理链路
```

### 🔍 **调试追踪要点**

如果您想追踪数据处理过程，可以关注以下关键点：

1. **中断频率**: 观察各个UART中断的触发频率
2. **缓冲区状态**: 检查环形缓冲区的数据长度变化
3. **解析成功率**: 统计协议解析的成功/失败次数
4. **数据更新频率**: 监控全局变量的更新频率
5. **PID输出范围**: 观察PID控制器的输出值范围
6. **电机响应**: 检查电机实际执行的速度和方向

**总结**: 整个系统通过中断驱动的数据接收、定时调度的数据处理、以及PID控制的闭环反馈，形成了一个完整的实时控制系统。每种数据都有其特定的处理链路，但最终都汇聚到全局变量中，供PID控制器使用。
```
